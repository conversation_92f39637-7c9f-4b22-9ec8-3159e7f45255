package webui

import (
	"fmt"
	"html/template"
	"net/http"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/Masterminds/sprig/v3"
	"github.vodafone.com/mikel-muennekhoff/argo-watch/internal/core"
)

func (s *service) HandleStatic() http.Handler {
	fs := http.FileServer(http.Dir("./internal/services/presentation/webui/static"))
	return http.StripPrefix("/static/", fs)
}

type dashboardData struct {
	Instances     []core.Instance
	Versions      []core.Version
	SearchOptions core.InstanceSearchOptions
	UpdateTypes   []string
}

func (s *service) HandleDashboard(w http.ResponseWriter, r *http.Request) {
	queryParams := r.URL.Query()
	queryText := queryParams.Get("text")
	queryKind := queryParams.Get("kind")

	search := false
	data := dashboardData{
		SearchOptions: core.InstanceSearchOptions{},
		UpdateTypes:   queryParams["updateType"],
	}

	if queryKind != "" {
		search = true
		data.SearchOptions.Kind = strings.Split(queryKind, ",")
	}

	if len(queryText) > 0 { // don't create a list with one empty string
		search = true
		data.SearchOptions.QualifierText = strings.Split(queryText, ",")
	}

	if search {
		versions, err := s.webServiceInjector.GetVersions()
		if err != nil {
			s.logger.Error("cannot get versions for dashboard", "msg", err.Error())
			// this is bad, but the dashboard can still show instances without the lastest versions
		}
		data.Versions = versions

		instances, err := s.webServiceInjector.GetInstances(data.SearchOptions)
		if err != nil {
			s.logger.Error("cannot get instances for dashboard", "msg", err.Error())
			http.Error(w, "an internal server error occurred", http.StatusInternalServerError)
			return
		}
		data.Instances = instances
	}

	templatePath := "./internal/services/presentation/webui/templates/index.html"
	b, err := os.ReadFile(templatePath)
	if err != nil {
		s.logger.Error("cannot open template", "error", err.Error(), "filepath", templatePath)
		http.Error(w, "an internal server error occurred", http.StatusInternalServerError)
		return
	}

	tmpl, err := template.New("index").Funcs(sprig.FuncMap()).Parse(string(b))
	if err != nil {
		s.logger.Error("cannot parse template", "error", err.Error())
		http.Error(w, "an internal server error occurred", http.StatusInternalServerError)
		return
	}

	err = tmpl.ExecuteTemplate(w, "index", data)
	if err != nil {
		s.logger.Error("cannot execute template", "error", err.Error())
		http.Error(w, "an internal server error occurred", http.StatusInternalServerError)
	}
}

// Badge cache to avoid repeated requests to ArgoCD
type badgeCache struct {
	mu    sync.RWMutex
	cache map[string]badgeCacheEntry
}

type badgeCacheEntry struct {
	data        []byte
	timestamp   time.Time
	contentType string
}

var globalBadgeCache = &badgeCache{
	cache: make(map[string]badgeCacheEntry),
}

func (s *service) HandleBadgeProxy(w http.ResponseWriter, r *http.Request) {
	environment := r.URL.Query().Get("environment")
	name := r.URL.Query().Get("name")

	if environment == "" || name == "" {
		http.Error(w, "environment and name parameters are required", http.StatusBadRequest)
		return
	}

	// Create cache key
	cacheKey := fmt.Sprintf("%s:%s", environment, name)

	// Check cache first
	globalBadgeCache.mu.RLock()
	if entry, exists := globalBadgeCache.cache[cacheKey]; exists {
		// Cache for 5 minutes
		if time.Since(entry.timestamp) < 5*time.Minute {
			globalBadgeCache.mu.RUnlock()
			w.Header().Set("Content-Type", entry.contentType)
			w.Header().Set("Cache-Control", "public, max-age=300") // 5 minutes
			w.Write(entry.data)
			return
		}
	}
	globalBadgeCache.mu.RUnlock()

	// Since ArgoCD badges require authentication, generate a custom status badge
	// based on the application data we have access to
	badge := s.generateStatusBadge(environment, name)

	// Cache the result
	globalBadgeCache.mu.Lock()
	globalBadgeCache.cache[cacheKey] = badgeCacheEntry{
		data:        []byte(badge),
		timestamp:   time.Now(),
		contentType: "image/svg+xml",
	}
	globalBadgeCache.mu.Unlock()

	// Return the badge
	w.Header().Set("Content-Type", "image/svg+xml")
	w.Header().Set("Cache-Control", "public, max-age=120") // 2 minutes
	w.Write([]byte(badge))
}

func (s *service) generateStatusBadge(environment, name string) string {
	// Try to find the application in our instances
	instances, err := s.webServiceInjector.GetInstances(core.InstanceSearchOptions{
		QualifierText: []string{environment, name},
	})

	var status, color string

	if err != nil || len(instances) == 0 {
		status = "Unknown"
		color = "#6c757d" // Gray
		s.logger.Debug("no instance found for badge", "environment", environment, "name", name)
	} else {
		// Found the application, determine status
		// Since we don't have health/sync status in the current data model,
		// we'll show a generic "Deployed" status
		status = "Deployed"
		color = "#28a745" // Green

		// You could extend this to check actual ArgoCD application status
		// if you have access to that data through the Kubernetes API
	}

	// Generate SVG badge
	statusWidth := len(status)*7 + 10 // Approximate width based on text length
	totalWidth := 60 + statusWidth

	badge := fmt.Sprintf(`<svg xmlns="http://www.w3.org/2000/svg" width="%d" height="20">
		<rect width="60" height="20" fill="#555"/>
		<rect x="60" width="%d" height="20" fill="%s"/>
		<text x="30" y="15" font-family="Arial, sans-serif" font-size="11" fill="white" text-anchor="middle">ArgoCD</text>
		<text x="%d" y="15" font-family="Arial, sans-serif" font-size="11" fill="white" text-anchor="middle">%s</text>
	</svg>`, totalWidth, statusWidth, color, 60+statusWidth/2, status)

	return badge
}
