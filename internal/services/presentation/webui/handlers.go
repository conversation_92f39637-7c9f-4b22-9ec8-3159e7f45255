package webui

import (
	"fmt"
	"html/template"
	"io"
	"net/http"
	"net/url"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/Masterminds/sprig/v3"
	"github.vodafone.com/mikel-muennekhoff/argo-watch/internal/core"
)

func (s *service) HandleStatic() http.Handler {
	fs := http.FileServer(http.Dir("./internal/services/presentation/webui/static"))
	return http.StripPrefix("/static/", fs)
}

type dashboardData struct {
	Instances     []core.Instance
	Versions      []core.Version
	SearchOptions core.InstanceSearchOptions
	UpdateTypes   []string
}

func (s *service) HandleDashboard(w http.ResponseWriter, r *http.Request) {
	queryParams := r.URL.Query()
	queryText := queryParams.Get("text")
	queryKind := queryParams.Get("kind")

	search := false
	data := dashboardData{
		SearchOptions: core.InstanceSearchOptions{},
		UpdateTypes:   queryParams["updateType"],
	}

	if queryKind != "" {
		search = true
		data.SearchOptions.Kind = strings.Split(queryKind, ",")
	}

	if len(queryText) > 0 { // don't create a list with one empty string
		search = true
		data.SearchOptions.QualifierText = strings.Split(queryText, ",")
	}

	if search {
		versions, err := s.webServiceInjector.GetVersions()
		if err != nil {
			s.logger.Error("cannot get versions for dashboard", "msg", err.Error())
			// this is bad, but the dashboard can still show instances without the lastest versions
		}
		data.Versions = versions

		instances, err := s.webServiceInjector.GetInstances(data.SearchOptions)
		if err != nil {
			s.logger.Error("cannot get instances for dashboard", "msg", err.Error())
			http.Error(w, "an internal server error occurred", http.StatusInternalServerError)
			return
		}
		data.Instances = instances
	}

	templatePath := "./internal/services/presentation/webui/templates/index.html"
	b, err := os.ReadFile(templatePath)
	if err != nil {
		s.logger.Error("cannot open template", "error", err.Error(), "filepath", templatePath)
		http.Error(w, "an internal server error occurred", http.StatusInternalServerError)
		return
	}

	tmpl, err := template.New("index").Funcs(sprig.FuncMap()).Parse(string(b))
	if err != nil {
		s.logger.Error("cannot parse template", "error", err.Error())
		http.Error(w, "an internal server error occurred", http.StatusInternalServerError)
		return
	}

	err = tmpl.ExecuteTemplate(w, "index", data)
	if err != nil {
		s.logger.Error("cannot execute template", "error", err.Error())
		http.Error(w, "an internal server error occurred", http.StatusInternalServerError)
	}
}

// Badge cache to avoid repeated requests to ArgoCD
type badgeCache struct {
	mu    sync.RWMutex
	cache map[string]badgeCacheEntry
}

type badgeCacheEntry struct {
	data        []byte
	timestamp   time.Time
	contentType string
}

var globalBadgeCache = &badgeCache{
	cache: make(map[string]badgeCacheEntry),
}

func (s *service) HandleBadgeProxy(w http.ResponseWriter, r *http.Request) {
	environment := r.URL.Query().Get("environment")
	name := r.URL.Query().Get("name")

	if environment == "" || name == "" {
		http.Error(w, "environment and name parameters are required", http.StatusBadRequest)
		return
	}

	// Create cache key
	cacheKey := fmt.Sprintf("%s:%s", environment, name)

	// Check cache first
	globalBadgeCache.mu.RLock()
	if entry, exists := globalBadgeCache.cache[cacheKey]; exists {
		// Cache for 5 minutes
		if time.Since(entry.timestamp) < 5*time.Minute {
			globalBadgeCache.mu.RUnlock()
			w.Header().Set("Content-Type", entry.contentType)
			w.Header().Set("Cache-Control", "public, max-age=300") // 5 minutes
			w.Write(entry.data)
			return
		}
	}
	globalBadgeCache.mu.RUnlock()

	// Build ArgoCD badge URL
	badgeURL := fmt.Sprintf("https://%s.mgmt.pub.sol-vf.de/api/badge?name=%s&namespace=%s",
		url.QueryEscape(environment),
		url.QueryEscape(name),
		url.QueryEscape(environment))

	// Fetch badge from ArgoCD
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	resp, err := client.Get(badgeURL)
	if err != nil {
		s.logger.Error("failed to fetch badge", "url", badgeURL, "error", err.Error())
		http.Error(w, "failed to fetch badge", http.StatusBadGateway)
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		s.logger.Error("badge request failed", "url", badgeURL, "status", resp.StatusCode)
		http.Error(w, "badge not available", http.StatusBadGateway)
		return
	}

	// Read response
	data, err := io.ReadAll(resp.Body)
	if err != nil {
		s.logger.Error("failed to read badge response", "url", badgeURL, "error", err.Error())
		http.Error(w, "failed to read badge", http.StatusBadGateway)
		return
	}

	contentType := resp.Header.Get("Content-Type")
	if contentType == "" {
		contentType = "image/svg+xml" // Default for ArgoCD badges
	}

	// Debug: Log what we received
	previewLen := len(data)
	if previewLen > 200 {
		previewLen = 200
	}
	s.logger.Info("badge proxy response",
		"url", badgeURL,
		"contentType", contentType,
		"dataLength", len(data),
		"dataPreview", string(data[:previewLen]))

	// Check if response looks like HTML (authentication redirect)
	dataStr := string(data)
	if strings.Contains(strings.ToLower(dataStr), "<html") ||
		strings.Contains(strings.ToLower(dataStr), "<!doctype") ||
		strings.Contains(dataStr, "oauth2") ||
		strings.Contains(dataStr, "login") {
		s.logger.Error("received HTML instead of badge, likely authentication required", "url", badgeURL)

		// Return a fallback badge indicating authentication is required
		fallbackBadge := `<svg xmlns="http://www.w3.org/2000/svg" width="120" height="20">
			<rect width="120" height="20" fill="#e74c3c"/>
			<text x="60" y="15" font-family="Arial" font-size="11" fill="white" text-anchor="middle">Auth Required</text>
		</svg>`

		w.Header().Set("Content-Type", "image/svg+xml")
		w.Header().Set("Cache-Control", "public, max-age=60") // Cache for 1 minute
		w.Write([]byte(fallbackBadge))
		return
	}

	// Cache the result
	globalBadgeCache.mu.Lock()
	globalBadgeCache.cache[cacheKey] = badgeCacheEntry{
		data:        data,
		timestamp:   time.Now(),
		contentType: contentType,
	}
	globalBadgeCache.mu.Unlock()

	// Return the badge
	w.Header().Set("Content-Type", contentType)
	w.Header().Set("Cache-Control", "public, max-age=300") // 5 minutes
	w.Write(data)
}
