package webui

import (
	"fmt"
	"html/template"
	"net/http"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/Masterminds/sprig/v3"
	"github.vodafone.com/mikel-muennekhoff/argo-watch/internal/core"
)

func (s *service) HandleStatic() http.Handler {
	fs := http.FileServer(http.Dir("./internal/services/presentation/webui/static"))
	return http.StripPrefix("/static/", fs)
}

type dashboardData struct {
	Instances     []core.Instance
	Versions      []core.Version
	SearchOptions core.InstanceSearchOptions
	UpdateTypes   []string
}

func (s *service) HandleDashboard(w http.ResponseWriter, r *http.Request) {
	queryParams := r.URL.Query()
	queryText := queryParams.Get("text")
	queryKind := queryParams.Get("kind")

	search := false
	data := dashboardData{
		SearchOptions: core.InstanceSearchOptions{},
		UpdateTypes:   queryParams["updateType"],
	}

	if queryKind != "" {
		search = true
		data.SearchOptions.Kind = strings.Split(queryKind, ",")
	}

	if len(queryText) > 0 { // don't create a list with one empty string
		search = true
		data.SearchOptions.QualifierText = strings.Split(queryText, ",")
	}

	if search {
		versions, err := s.webServiceInjector.GetVersions()
		if err != nil {
			s.logger.Error("cannot get versions for dashboard", "msg", err.Error())
			// this is bad, but the dashboard can still show instances without the lastest versions
		}
		data.Versions = versions

		instances, err := s.webServiceInjector.GetInstances(data.SearchOptions)
		if err != nil {
			s.logger.Error("cannot get instances for dashboard", "msg", err.Error())
			http.Error(w, "an internal server error occurred", http.StatusInternalServerError)
			return
		}
		data.Instances = instances
	}

	templatePath := "./internal/services/presentation/webui/templates/index.html"
	b, err := os.ReadFile(templatePath)
	if err != nil {
		s.logger.Error("cannot open template", "error", err.Error(), "filepath", templatePath)
		http.Error(w, "an internal server error occurred", http.StatusInternalServerError)
		return
	}

	tmpl, err := template.New("index").Funcs(sprig.FuncMap()).Parse(string(b))
	if err != nil {
		s.logger.Error("cannot parse template", "error", err.Error())
		http.Error(w, "an internal server error occurred", http.StatusInternalServerError)
		return
	}

	err = tmpl.ExecuteTemplate(w, "index", data)
	if err != nil {
		s.logger.Error("cannot execute template", "error", err.Error())
		http.Error(w, "an internal server error occurred", http.StatusInternalServerError)
	}
}

const (
	// Badge cache duration in minutes
	badgeCacheDuration = 5 * time.Minute
	// Badge cache control header value in seconds
	badgeCacheControlMaxAge = 300 // 5 minutes
)

// badgeCache provides thread-safe caching for generated status badges
type badgeCache struct {
	mu    sync.RWMutex
	cache map[string]badgeCacheEntry
}

type badgeCacheEntry struct {
	data      []byte
	timestamp time.Time
}

var globalBadgeCache = &badgeCache{
	cache: make(map[string]badgeCacheEntry),
}

// HandleBadgeProxy generates custom ArgoCD status badges based on real application data
// This replaces the official ArgoCD badge API to avoid authentication issues
func (s *service) HandleBadgeProxy(w http.ResponseWriter, r *http.Request) {
	environment := r.URL.Query().Get("environment")
	name := r.URL.Query().Get("name")

	if environment == "" || name == "" {
		http.Error(w, "environment and name parameters are required", http.StatusBadRequest)
		return
	}

	// Create cache key
	cacheKey := fmt.Sprintf("%s:%s", environment, name)

	// Check cache first
	globalBadgeCache.mu.RLock()
	if entry, exists := globalBadgeCache.cache[cacheKey]; exists {
		if time.Since(entry.timestamp) < badgeCacheDuration {
			globalBadgeCache.mu.RUnlock()
			w.Header().Set("Content-Type", "image/svg+xml")
			w.Header().Set("Cache-Control", fmt.Sprintf("public, max-age=%d", badgeCacheControlMaxAge))
			w.Write(entry.data)
			return
		}
	}
	globalBadgeCache.mu.RUnlock()

	// Generate custom status badge based on real ArgoCD application data
	badge := s.generateStatusBadge(environment, name)

	// Cache the result
	globalBadgeCache.mu.Lock()
	globalBadgeCache.cache[cacheKey] = badgeCacheEntry{
		data:      []byte(badge),
		timestamp: time.Now(),
	}
	globalBadgeCache.mu.Unlock()

	// Return the badge
	w.Header().Set("Content-Type", "image/svg+xml")
	w.Header().Set("Cache-Control", fmt.Sprintf("public, max-age=%d", badgeCacheControlMaxAge))
	w.Write([]byte(badge))
}

// generateStatusBadge creates a custom SVG badge based on real ArgoCD application status
func (s *service) generateStatusBadge(environment, name string) string {
	status, color := s.getApplicationStatus(environment, name)
	return s.createSVGBadge(status, color)
}

// getApplicationStatus retrieves the real ArgoCD application status from our data
func (s *service) getApplicationStatus(environment, name string) (string, string) {
	instances, err := s.webServiceInjector.GetInstances(core.InstanceSearchOptions{
		QualifierText: []string{environment, name},
	})

	if err != nil || len(instances) == 0 {
		return "Unknown", "#6c757d" // Gray
	}

	// Find the exact matching instance
	for _, instance := range instances {
		if instance.Qualifiers["environment"] == environment && instance.Qualifiers["name"] == name {
			healthStatus := instance.Parameters["_health_status"]
			syncStatus := instance.Parameters["_sync_status"]
			return s.determineStatusAndColor(healthStatus, syncStatus)
		}
	}

	return "Unknown", "#6c757d" // Gray
}

// createSVGBadge generates the SVG badge with the given status and color
func (s *service) createSVGBadge(status, color string) string {
	statusWidth := len(status)*7 + 10 // Approximate width based on text length
	totalWidth := 60 + statusWidth

	return fmt.Sprintf(`<svg xmlns="http://www.w3.org/2000/svg" width="%d" height="20">
		<rect width="60" height="20" fill="#555"/>
		<rect x="60" width="%d" height="20" fill="%s"/>
		<text x="30" y="15" font-family="Arial, sans-serif" font-size="11" fill="white" text-anchor="middle">ArgoCD</text>
		<text x="%d" y="15" font-family="Arial, sans-serif" font-size="11" fill="white" text-anchor="middle">%s</text>
	</svg>`, totalWidth, statusWidth, color, 60+statusWidth/2, status)
}

// determineStatusAndColor maps ArgoCD health and sync status to badge display values
// Priority: Health status is more critical than sync status
func (s *service) determineStatusAndColor(healthStatus, syncStatus string) (string, string) {
	switch strings.ToLower(healthStatus) {
	case "healthy":
		// Healthy applications: check sync status for final determination
		if strings.ToLower(syncStatus) == "outofsync" {
			return "OutOfSync", "#ffc107" // Yellow - needs sync
		}
		return "Healthy", "#28a745" // Green - all good
	case "progressing":
		return "Progressing", "#17a2b8" // Blue - deployment in progress
	case "degraded":
		return "Degraded", "#dc3545" // Red - has issues
	case "suspended":
		return "Suspended", "#6c757d" // Gray - intentionally stopped
	case "missing":
		return "Missing", "#dc3545" // Red - resources missing
	case "unknown":
		// Health unknown: fall back to sync status
		return s.mapSyncStatus(syncStatus)
	default:
		// Unrecognized health status: fall back to sync status
		return s.mapSyncStatus(syncStatus)
	}
}

// mapSyncStatus maps ArgoCD sync status to badge display values
func (s *service) mapSyncStatus(syncStatus string) (string, string) {
	switch strings.ToLower(syncStatus) {
	case "synced":
		return "Synced", "#28a745" // Green - in sync
	case "outofsync":
		return "OutOfSync", "#ffc107" // Yellow - needs sync
	default:
		return "Unknown", "#6c757d" // Gray - status unknown
	}
}
