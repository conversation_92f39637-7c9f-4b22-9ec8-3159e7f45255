package argoapps

import (
	"fmt"
	"net/url"

	"log/slog"

	"github.vodafone.com/mikel-muennekhoff/argo-watch/internal/core"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/dynamic/dynamicinformer"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/cache"
	"k8s.io/client-go/tools/clientcmd"
)

var kubeSchema *schema.GroupVersionResource

func init() {
	kubeSchema = &schema.GroupVersionResource{
		Group:    "argoproj.io",
		Version:  "v1alpha1", // TODO support multiple versions
		Resource: "applications",
	}
}

type service struct {
	logger                   *slog.Logger
	args                     *ServiceArguments
	coreFuncs                core.DataServiceInjector
	kube                     *dynamic.DynamicClient
	staticInstanceQualifiers map[string]string
	staticInstanceParameters map[string]string
	deferFuncs               []func()
}

type ServiceArguments struct {
	KubeConfigFilePath       string
	KubeConfigName           string
	StaticInstanceQualifiers map[string]string // these are added to all instances (can be overridden)
	StaticInstanceParameters map[string]string // these are added to all instances (can be overridden)
}

func NewService(args *ServiceArguments) (core.Service, error) {
	s := service{
		args: args,
	}

	if args.StaticInstanceQualifiers != nil {
		s.staticInstanceQualifiers = args.StaticInstanceQualifiers
	} else {
		s.staticInstanceQualifiers = make(map[string]string)
	}

	if args.StaticInstanceParameters != nil {
		s.staticInstanceParameters = args.StaticInstanceParameters
	} else {
		s.staticInstanceParameters = make(map[string]string)
	}

	return &s, nil
}

func (s *service) Inject(injector core.DataServiceInjector) {
	s.coreFuncs = injector
}

func (s *service) InjectLogger(logger *slog.Logger) {
	s.logger = logger
}

func (s *service) Start() error {

	var cfg *rest.Config
	var err error

	if s.args.KubeConfigFilePath != "" {
		s.logger.Info("reading kube config from file", "path", s.args.KubeConfigFilePath, "name", s.args.KubeConfigName)
		apiCfg, err := clientcmd.LoadFromFile(s.args.KubeConfigFilePath)
		if err != nil {
			return fmt.Errorf("cannot load kube config file from %s: %w", s.args.KubeConfigFilePath, err)
		}

		apiCfg.CurrentContext = s.args.KubeConfigName

		cfg, err = clientcmd.NewDefaultClientConfig(*apiCfg, &clientcmd.ConfigOverrides{}).ClientConfig()
		if err != nil {
			return fmt.Errorf("cannot create kube config: %w", err)
		}
	} else {
		s.logger.Info("no kube config file path configured, falling back to in-cluster config")
		cfg, err = rest.InClusterConfig()
		if err != nil {
			return fmt.Errorf("cannot load in-cluster kube config: %w", err)
		}
	}

	client, err := dynamic.NewForConfig(cfg)
	if err != nil {
		return fmt.Errorf("cannot create kube client from config: %w", err)
	}

	s.kube = client

	dynInformer := dynamicinformer.NewDynamicSharedInformerFactory(s.kube, 0)
	informer := dynInformer.ForResource(*kubeSchema).Informer()
	informer.AddEventHandler(cache.ResourceEventHandlerFuncs{
		AddFunc: func(obj interface{}) {
			instance, err := s.InformerObjToInstance(obj)
			if err != nil {
				s.logger.Error("failed to parse unstructured to instance", "msg", err.Error())
				return
			}
			if err = s.coreFuncs.StoreInstance(instance); err != nil {
				s.logger.Error("failed to store instance", "msg", err.Error(), "instance", instance)
				return
			}
			s.logger.Debug("stored new instance", "instance", instance.SlogAttributes())
		},
		UpdateFunc: func(_, obj interface{}) {
			instance, err := s.InformerObjToInstance(obj)
			if err != nil {
				s.logger.Error("failed to parse unstructured to instance", "msg", err.Error())
				return
			}
			if err = s.coreFuncs.StoreInstance(instance); err != nil {
				s.logger.Error("failed to store instance", "msg", err.Error(), "instance", instance)
				return
			}
			s.logger.Debug("stored changed instance", "instance", instance.SlogAttributes())
		},
		DeleteFunc: func(obj interface{}) {
			instance, err := s.InformerObjToInstance(obj)
			if err != nil {
				s.logger.Error("failed to parse unstructured to instance", "msg", err.Error())
				return
			}
			if err = s.coreFuncs.DeleteInstance(instance); err != nil {
				s.logger.Error("failed to store instance", "msg", err.Error(), "instance", instance)
				return
			}
			s.logger.Debug("deleted instance", "instance", instance.SlogAttributes())
		},
	})

	stopChan := make(chan struct{}, 1)
	s.logger.Info("start argoapps watcher")
	go informer.Run(stopChan)

	s.deferFuncs = append(s.deferFuncs, func() {
		s.logger.Info("trigger stopping argoapps watcher")
		stopChan <- struct{}{}
	})

	return nil
}

func (s *service) Stop() error {
	for _, fn := range s.deferFuncs {
		fn()
	}
	return nil
}

func (s *service) ID() string {
	return "argoapps"
}

func (s *service) InstanceHandlers() []core.InstanceHandler {
	return []core.InstanceHandler{}
}

func (s *service) InformerObjToInstance(raw interface{}) (core.Instance, error) {
	var instance core.Instance
	u, ok := raw.(*unstructured.Unstructured)
	if !ok {
		return instance, fmt.Errorf("cannot cast object to unstructured")
	}

	return s.UnstructuredAppToInstance(u)

}

func (s *service) UnstructuredAppToInstance(raw *unstructured.Unstructured) (core.Instance, error) {
	instance := core.Instance{}
	repoURL, found, err := unstructured.NestedFieldNoCopy(raw.Object, "spec", "source", "repoURL")
	if err != nil {
		return instance, fmt.Errorf("error while getting spec.source.repoURL from raw event object: %w", err)
	}
	if !found {
		return instance, fmt.Errorf("did not find spec.source.repoURL in raw event object: %v", raw)
	}
	repo := core.RepositoryFromURL(repoURL.(string))

	value, found, err := unstructured.NestedFieldNoCopy(raw.Object, "spec", "source", "targetRevision")
	if err != nil {
		return instance, fmt.Errorf("cannot get targetRevision of argo app: %w", err)
	}
	if !found {
		return instance, fmt.Errorf("did not find targetRevision of argo app")
	} else {
		instance.Version = core.VersionFromString(repo, value.(string))
	}

	qualifiers := make(map[string]string)
	for key, value := range s.staticInstanceQualifiers {
		qualifiers[key] = value
	}

	parameters := make(map[string]string)
	for key, value := range s.staticInstanceParameters {
		parameters[key] = value
	}

	value, found, err = unstructured.NestedFieldNoCopy(raw.Object, "metadata", "namespace")
	if err != nil {
		return instance, fmt.Errorf("cannot get namespace of argo app: %w", err)
	}
	if !found {
		return instance, fmt.Errorf("did not find namespace of argo app")
	} else {
		qualifiers["environment"] = value.(string)
	}

	value, found, err = unstructured.NestedFieldNoCopy(raw.Object, "metadata", "name")
	if err != nil {
		qualifiers["name"] = "error"
	}
	if !found {
		qualifiers["name"] = "N/A"
	} else {
		qualifiers["name"] = value.(string)
	}

	value, found, err = unstructured.NestedFieldNoCopy(raw.Object, "spec", "source", "path")
	if err != nil {
		s.logger.Warn("cannot get repository path of argo app", "msg", err.Error(), "instance", instance.SlogAttributes())
	}
	if !found {
		s.logger.Warn("did not find repository path of argo app", "instance", instance.SlogAttributes())
	} else {
		repoPath := value.(string)
		parameters["repoPath"] = repoPath
		parameters["chart@master"] = fmt.Sprintf("%s/tree/master/%s", repo.URL, repoPath)
	}

	value, found, err = unstructured.NestedFieldNoCopy(raw.Object, "metadata", "labels", "argocd.argoproj.io/instance")
	if err != nil {
		s.logger.Warn("cannot get parent app label of argo app", "msg", err.Error(), "instance", instance.SlogAttributes())
	}
	if !found {
		s.logger.Debug("did not find parent app label of argo app", "instance", instance.SlogAttributes())
	} else {
		parameters["parent"] = value.(string)
	}

	parameters["argo-badge"] = fmt.Sprintf("img:https://%s.mgmt.pub.sol-vf.de/api/badge?name=%s", url.QueryEscape(qualifiers["environment"]), url.QueryEscape(qualifiers["name"]))
	parameters["ArgoCD UI"] = fmt.Sprintf("https://%s.mgmt.pub.sol-vf.de/applications/%s/%s", url.QueryEscape(qualifiers["environment"]), url.QueryEscape(qualifiers["environment"]), url.QueryEscape(qualifiers["name"]))

	instance.Qualifiers = qualifiers
	instance.Parameters = parameters

	return instance, nil
}

func (s *service) HandleInstance(_ core.Instance) error {
	return nil
}
